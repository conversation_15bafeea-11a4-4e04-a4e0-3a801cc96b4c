#!/bin/bash
# ./apk_upload.sh

# 1. 取得最新 Git commit message
#GIT_MESSAGE=$(git log -1 --pretty=%B)
# 1. 取得最新五筆 Git commit messages（每筆一行）
GIT_MESSAGES=$(git log -30 --pretty=format:"- %s")

# 2. 寫入到 RELEASE_NOTE 檔案
echo "Release Note:" > RELEASE_NOTE.txt
echo "$GIT_MESSAGES" >> RELEASE_NOTE.txt

# 專案資訊  % chmod +x apk_upload.sh ./apk_upload.sh
APP_ID="1:470077449550:android:4971c9e15686127296aa1f"  # ← 換成你的 Firebase Android App ID
TESTERS="<EMAIL>"     # ← 可用逗號分隔
RELEASE_NOTE="$GIT_MESSAGES"

# 防呆檢查
command -v flutter >/dev/null 2>&1 || { echo "❌ Flutter 未安裝"; exit 1; }
command -v firebase >/dev/null 2>&1 || { echo "❌ Firebase CLI 未安裝"; exit 1; }

# 讀取 pubspec.yaml 中的版本號
VERSION=$(grep '^version:' pubspec.yaml | awk '{print $2}' | cut -d "+" -f1)
BUILD=$(grep '^version:' pubspec.yaml | awk '{print $2}' | cut -d "+" -f2)

# 打包 APK
flutter build apk --debug
#flutter build apk --release

# 重新命名 APK
APK_NAME="Astreal_debug_v${VERSION}+${BUILD}.apk"
mv build/app/outputs/flutter-apk/app-debug.apk build/app/outputs/flutter-apk/${APK_NAME}

# 上傳到 Firebase App Distribution
firebase appdistribution:distribute build/app/outputs/flutter-apk/${APK_NAME} \
  --app $APP_ID \
  --release-notes "$RELEASE_NOTE" \
  --testers "$TESTERS"

echo "✅ 已上傳 ${APK_NAME} 至 Firebase Distribution 🎉"

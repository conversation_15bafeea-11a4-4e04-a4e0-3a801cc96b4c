import 'dart:ui';

import 'package:flutter/material.dart';

import '../../data/models/astrology/arabic_point.dart';
import '../../data/models/astrology/aspect.dart';
import '../../data/models/astrology/planet.dart';
import '../../features/astrology/constants/zodiac_definitions.dart';

/// 占星常量类
/// 存储行星、相位、星座等常量定义
class AstrologyConstants {
  // 行星常數定義
  static const int SUN = 0;
  static const int MOON = 1;
  static const int MERCURY = 2;
  static const int VENUS = 3;
  static const int MARS = 4;
  static const int JUPITER = 5;
  static const int SATURN = 6;
  static const int URANUS = 7;
  static const int NEPTUNE = 8;
  static const int PLUTO = 9;

  // 特殊點位常數定義
  static const int ASCENDANT = 30; // 上升點
  static const int MIDHEAVEN = 31; // 中天
  static const int DESCENDANT = 32; // 下降點
  static const int IMUM_COELI = 33; // 天底

  // 行星定義表 - 保持向後兼容的 Map 格式
  static List<Map<String, dynamic>> get PLANETS => _generatePlanetsMap();

  // 新的類型安全的行星列表
  static List<Planet> get PLANETS_TYPED => _generatePlanets();

  /// 生成行星 Map 列表（向後兼容）
  static List<Map<String, dynamic>> _generatePlanetsMap() {
    return _generatePlanets().map((planet) => planet.toMap()).toList();
  }

  /// 生成行星列表
  static List<Planet> _generatePlanets() {
    return [
      Planet(
        id: SUN,
        name: '太陽',
        englishName: 'Sun',
        symbol: '☉',
        description: '核心自我、生命力、意識、父親形象',
        color: const Color(0xFFFF0000),
        category: 'luminary',
        keywords: const ['自我', '意識', '生命力', '權威', '創造力'],
      ),
      Planet(
        id: MOON,
        name: '月亮',
        englishName: 'Moon',
        symbol: '☽',
        description: '情感、潛意識、本能反應、母親形象',
        color: const Color(0xFF0A0AFF),
        category: 'luminary',
        keywords: const ['情感', '直覺', '記憶', '家庭', '安全感'],
      ),
      Planet(
        id: MERCURY,
        name: '水星',
        englishName: 'Mercury',
        symbol: '☿',
        description: '思維、溝通、學習能力、資訊處理',
        color: const Color(0xFF127116),
        category: 'personal',
        keywords: const ['思維', '溝通', '學習', '適應', '分析'],
      ),
      Planet(
        id: VENUS,
        name: '金星',
        englishName: 'Venus',
        symbol: '♀',
        description: '愛情、美感、價值觀、藝術',
        color: const Color(0xFFCA9833),
        category: 'personal',
        keywords: const ['愛情', '美感', '和諧', '價值', '享受'],
      ),
      Planet(
        id: MARS,
        name: '火星',
        englishName: 'Mars',
        symbol: '♂',
        description: '行動力、慾望、競爭力、勇氣',
        color: const Color(0xFFFF0000),
        category: 'personal',
        keywords: const ['行動', '慾望', '競爭', '勇氣', '衝動'],
      ),
      Planet(
        id: JUPITER,
        name: '木星',
        englishName: 'Jupiter',
        symbol: '♃',
        description: '擴張、幸運、信念、智慧',
        color: const Color(0xFFFF0000),
        category: 'social',
        keywords: const ['擴張', '幸運', '信念', '智慧', '成長'],
      ),
      Planet(
        id: SATURN,
        name: '土星',
        englishName: 'Saturn',
        symbol: '♄',
        description: '限制、責任、紀律、結構',
        color: const Color(0xFFCA9833),
        category: 'social',
        keywords: const ['責任', '紀律', '限制', '結構', '成熟'],
      ),
      Planet(
        id: URANUS,
        name: '天王星',
        englishName: 'Uranus',
        symbol: '♅',
        description: '革新、獨立、突破、科技',
        color: const Color(0xFF00FFFF),
        category: 'transpersonal',
        keywords: const ['革新', '獨立', '突破', '科技', '自由'],
      ),
      Planet(
        id: NEPTUNE,
        name: '海王星',
        englishName: 'Neptune',
        symbol: '♆',
        description: '靈性、夢想、幻象、同情',
        color: const Color(0xFF0000FF),
        category: 'transpersonal',
        keywords: const ['靈性', '夢想', '直覺', '同情', '犧牲'],
      ),
      Planet(
        id: PLUTO,
        name: '冥王星',
        englishName: 'Pluto',
        symbol: '♇',
        description: '轉化、重生、深度、權力',
        color: const Color(0xFF800080),
        category: 'transpersonal',
        keywords: const ['轉化', '重生', '深度', '權力', '極端'],
      ),
    ];
  }


  // 相位定義表 - 保持向後兼容的 Map 格式
  static List<Map<String, dynamic>> get ASPECTS => _generateAspectsMap();

  // 新的類型安全的相位列表
  static List<Aspect> get ASPECTS_TYPED => _generateAspects();

  /// 生成相位 Map 列表（向後兼容）
  static List<Map<String, dynamic>> _generateAspectsMap() {
    return _generateAspects().map((aspect) => aspect.toMap()).toList();
  }

  /// 生成相位列表
  static List<Aspect> _generateAspects() {
    return const [
      Aspect(
        name: '合相',
        shortName: 'Conj',
        shortZh: '合',
        angle: 0,
        orb: 8.0,
        symbol: '☌',
        color: Color(0xFF224EA5),
        nature: 'neutral',
        keywords: ['融合', '開始', '強化', '統一'],
        description: '兩個行星在同一位置，能量融合',
      ),
      Aspect(
        name: '六分相',
        shortName: 'Sext',
        shortZh: '六合',
        angle: 60,
        orb: 4.0,
        symbol: '⚹',
        color: Color(0xFF2999A4),
        nature: 'harmonious',
        keywords: ['機會', '才能', '合作', '發展'],
        description: '和諧的能量流動，帶來機會和才能',
      ),
      Aspect(
        name: '四分相',
        shortName: 'Sqr',
        shortZh: '刑',
        angle: 90,
        orb: 6.0,
        symbol: '□',
        color: Color(0xFFE74C3C),
        nature: 'challenging',
        keywords: ['挑戰', '緊張', '行動', '突破'],
        description: '緊張的能量，需要努力整合和突破',
      ),
      Aspect(
        name: '三分相',
        shortName: 'Tri',
        shortZh: '拱',
        angle: 120,
        orb: 6.0,
        symbol: '△',
        color: Color(0xFF27AE60),
        nature: 'harmonious',
        keywords: ['和諧', '天賦', '流暢', '幸運'],
        description: '最和諧的相位，能量流暢自然',
      ),
      Aspect(
        name: '對分相',
        shortName: 'Opp',
        shortZh: '沖',
        angle: 180,
        orb: 8.0,
        symbol: '☍',
        color: Color(0xFF8E44AD),
        nature: 'challenging',
        keywords: ['對立', '平衡', '投射', '整合'],
        description: '對立的能量，需要尋求平衡和整合',
      ),
    ];
  }



  // 星座定義表
  static const List<String> ZODIAC_SIGNS = [
    '牡羊座',
    '金牛座',
    '雙子座',
    '巨蟹座',
    '獅子座',
    '處女座',
    '天秤座',
    '天蠍座',
    '射手座',
    '摩羯座',
    '水瓶座',
    '雙魚座'
  ];

  // 星座符號表 - 使用 ZodiacDefinitions 作為統一來源
  static Map<String, String> get ZODIAC_SYMBOLS {
    final symbols = <String, String>{};
    for (final sign in ZodiacDefinitions.zodiacSigns) {
      symbols[sign['name'] as String] = sign['symbol'] as String;
    }
    return symbols;
  }

  // 阿拉伯點定義
  static const int FORTUNE_POINT = 100; // 幸運點/福點
  static const int SPIRIT_POINT = 101; // 精神點
  static const int EXALTATION_POINT = 102; // 擢升點/旺點
  static const int BASIS_POINT = 103; // 基礎點
  static const int CHILDREN_POINT = 104; // 子女點
  static const int MARRIAGE_POINT = 105; // 婚姻點
  static const int SUCCESS_POINT = 106; // 成功點
  static const int PROFESSION_POINT = 107; // 職業點
  static const int FATHER_POINT = 108; // 父親點
  static const int MOTHER_POINT = 109; // 母親點
  static const int DEBT_POINT = 110; // 債務點
  static const int LOVE_POINT = 111; // 愛情點
  static const int MARRIAGE_MALE_POINT = 112; // 男性婚姻點
  static const int MARRIAGE_FEMALE_POINT = 113; // 女性婚姻點
  static const int SON_POINT = 114; // 兒子點
  static const int DAUGHTER_POINT = 115; // 女兒點
  static const int BROTHER_POINT = 116; // 兄弟姐妹點
  static const int SPECULATION_POINT = 117; // 投機點
  static const int NEMESIS_POINT = 118; // 復仇點
  static const int DEATH_POINT = 119; // 死亡點
  static const int THEFT_POINT = 120; // 盜竊點
  static const int TRAGEDY_POINT = 121; // 悲劇點
  static const int HIGHER_EDUCATION_POINT = 122; // 高等教育點
  static const int ASSASSINATION_POINT = 123; // 暗殺點
  static const int FRIENDS_POINT = 124; // 朋友點
  static const int WORK_POINT = 125; // 工作點

  // 新增的阿拉伯點
  static const int SUN_MOON_MIDPOINT = 61; // 日月中點
  static const int VERTEX_POINT = 62; // 宿命點

  // static const int MONEY_POINT = 129; // 金錢點
  // static const int KNOWLEDGE_POINT = 200; // 知識點

  // 阿拉伯點定義表 - 保持向後兼容的 Map 格式
  static List<Map<String, dynamic>> getArabicPoints() {
    return getArabicPointsTyped().map((point) => point.toMap()).toList();
  }

  // 新的類型安全的阿拉伯點列表
  static List<ArabicPoint> getArabicPointsTyped() {
    return [
      // 新增的特殊點
      const ArabicPoint(
        id: SUN_MOON_MIDPOINT,
        name: '日月中點',
        englishName: 'Sun-Moon Midpoint',
        symbol: 'V',
        description: '日月中點（Sun-Moon Midpoint）代表個人內在的平衡點，融合了太陽的意識與月亮的潛意識，象徵個性的核心與情感的統一。',
        formula: '(太陽 + 月亮) / 2',
        dayFormula: '(太陽 + 月亮) / 2',
        nightFormula: '(太陽 + 月亮) / 2',
        category: 'special',
        importance: 0.8,
        keywords: ['平衡', '統一', '核心', '整合'],
        relatedPlanets: ['太陽', '月亮'],
        isTimeDependent: false,
      ),
      const ArabicPoint(
        id: VERTEX_POINT,
        name: '宿命點',
        englishName: 'Vertex',
        symbol: 'Y',
        description: '宿命點（Vertex）被稱為命運的轉折點，代表重要的人際遇合與生命中的關鍵事件，常與業力和命定的相遇有關。',
        formula: '特殊計算',
        dayFormula: '特殊計算',
        nightFormula: '特殊計算',
        category: 'special',
        importance: 0.7,
        keywords: ['命運', '轉折', '相遇', '業力'],
        relatedPlanets: [],
        isTimeDependent: false,
      ),
      const ArabicPoint(
        id: FORTUNE_POINT,
        name: '幸運點',
        englishName: 'Lot of Fortune',
        symbol: '⊗',
        description: '幸運點（Lot of Fortune）是最古老且最著名的阿拉伯點之一，象徵一個人在生命中能夠找到幸福、滿足與財富的領域，並顯示天生的成功機會。',
        formula: '上升 + 月亮 - 太陽',
        dayFormula: '上升 + 月亮 - 太陽',
        nightFormula: '上升 + 太陽 - 月亮',
        category: 'major',
        importance: 1.0,
        keywords: ['幸福', '財富', '成功', '機會'],
        relatedPlanets: ['太陽', '月亮'],
        isTimeDependent: true,
      ),
      const ArabicPoint(
        id: SPIRIT_POINT,
        name: '精神點',
        englishName: 'Lot of Spirit',
        symbol: '⊛',
        description: '精神點（Lot of Spirit）與內在心靈追求有關，指引個人在精神層面尋求滿足、意義與才華展現的領域。',
        formula: '上升 + 太陽 - 月亮',
        dayFormula: '上升 + 太陽 - 月亮',
        nightFormula: '上升 + 月亮 - 太陽',
        category: 'major',
        importance: 0.9,
        keywords: ['精神', '心靈', '意義', '才華'],
        relatedPlanets: ['太陽', '月亮'],
        isTimeDependent: true,
      ),
      // TODO: 將其他阿拉伯點轉換為 ArabicPoint 對象
      // 暫時註釋掉其他點，避免編譯錯誤
    ];
  }

  // 阿拉伯點列表 - 保持向後兼容的 Map 格式
  static List<Map<String, dynamic>> get ARABIC_POINTS => _generateArabicPointsMap();

  // 新的類型安全的阿拉伯點列表
  static List<ArabicPoint> get ARABIC_POINTS_TYPED => _generateArabicPoints();

  /// 生成阿拉伯點 Map 列表（向後兼容）
  static List<Map<String, dynamic>> _generateArabicPointsMap() {
    return _generateArabicPoints().map((point) => point.toMap()).toList();
  }

  /// 生成阿拉伯點列表
  static List<ArabicPoint> _generateArabicPoints() {
    return const [
      ArabicPoint(
        id: FORTUNE_POINT,
        name: '福點',
        englishName: 'Part of Fortune',
        symbol: '⊗',
        description: '幸福、機遇、天賦才能的指標',
        formula: '上升 + 月亮 - 太陽',
        dayFormula: '上升 + 月亮 - 太陽',
        nightFormula: '上升 + 太陽 - 月亮',
        category: 'major',
        importance: 1.0,
        keywords: ['幸福', '機遇', '才能', '物質成功'],
        relatedPlanets: ['太陽', '月亮'],
        interpretation: '代表個人的幸福感和成功機會，是最重要的阿拉伯點',
      ),
      ArabicPoint(
        id: SPIRIT_POINT,
        name: '靈魂點',
        englishName: 'Part of Spirit',
        symbol: '☽',
        description: '精神層面的發展和靈性成長',
        formula: '上升 + 太陽 - 月亮',
        dayFormula: '上升 + 太陽 - 月亮',
        nightFormula: '上升 + 月亮 - 太陽',
        category: 'major',
        importance: 0.9,
        keywords: ['靈性', '精神', '意識', '成長'],
        relatedPlanets: ['太陽', '月亮'],
        interpretation: '代表精神層面的發展和靈性追求',
      ),
    ];
  }

  // 特殊度數定義
  static const double ARIES_EXALTATION_DEGREE = 19.0; // 太陽在牡羊座19度為最強旺
  static const double TAURUS_EXALTATION_DEGREE = 3.0; // 月亮在金牛座3度為最強旺

  // 星座定義
  static const String ARIES = '牡羊座';
  static const String TAURUS = '金牛座';
  static const String GEMINI = '雙子座';
  static const String CANCER = '巨蟹座';
  static const String LEO = '獅子座';
  static const String VIRGO = '處女座';
  static const String LIBRA = '天秤座';
  static const String SCORPIO = '天蠍座';
  static const String SAGITTARIUS = '射手座';
  static const String CAPRICORN = '摩羯座';
  static const String AQUARIUS = '水瓶座';
  static const String PISCES = '雙魚座';

  // 星座陰陽性定義
  // 陽性星座（陽陽陽）：白羊、雙子、獅子、天秤、射手、水瓶
  // 陰性星座（陰陰陰）：金牛、巨蟹、處女、天蠍、摩羯、雙魚
  static const Map<String, bool> SIGN_POLARITY = {
    ARIES: true,      // 牡羊座 - 陽性
    TAURUS: false,    // 金牛座 - 陰性
    GEMINI: true,     // 雙子座 - 陽性
    CANCER: false,    // 巨蟹座 - 陰性
    LEO: true,        // 獅子座 - 陽性
    VIRGO: false,     // 處女座 - 陰性
    LIBRA: true,      // 天秤座 - 陽性
    SCORPIO: false,   // 天蠍座 - 陰性
    SAGITTARIUS: true,// 射手座 - 陽性
    CAPRICORN: false, // 摩羯座 - 陰性
    AQUARIUS: true,   // 水瓶座 - 陽性
    PISCES: false,    // 雙魚座 - 陰性
  };

  // 星座元素定義
  // 火象星座：白羊、獅子、射手
  // 土象星座：金牛、處女、摩羯
  // 風象星座：雙子、天秤、水瓶
  // 水象星座：巨蟹、天蠍、雙魚
  static const Map<String, String> SIGN_ELEMENTS = {
    ARIES: '火',      // 牡羊座 - 火
    TAURUS: '土',     // 金牛座 - 土
    GEMINI: '風',     // 雙子座 - 風
    CANCER: '水',     // 巨蟹座 - 水
    LEO: '火',        // 獅子座 - 火
    VIRGO: '土',      // 處女座 - 土
    LIBRA: '風',      // 天秤座 - 風
    SCORPIO: '水',    // 天蠍座 - 水
    SAGITTARIUS: '火',// 射手座 - 火
    CAPRICORN: '土',  // 摩羯座 - 土
    AQUARIUS: '風',   // 水瓶座 - 風
    PISCES: '水',     // 雙魚座 - 水
  };

  // 星座品質定義
  // 基本/啟動星座：白羊、巨蟹、天秤、摩羯
  // 固定星座：金牛、獅子、天蠍、水瓶
  // 變動星座：雙子、處女、射手、雙魚
  static const Map<String, String> SIGN_MODALITIES = {
    ARIES: 'cardinal',      // 牡羊座 - 啟動
    TAURUS: 'fixed',        // 金牛座 - 固定
    GEMINI: 'mutable',      // 雙子座 - 變動
    CANCER: 'cardinal',     // 巨蟹座 - 啟動
    LEO: 'fixed',           // 獅子座 - 固定
    VIRGO: 'mutable',       // 處女座 - 變動
    LIBRA: 'cardinal',      // 天秤座 - 啟動
    SCORPIO: 'fixed',       // 天蠍座 - 固定
    SAGITTARIUS: 'mutable', // 射手座 - 變動
    CAPRICORN: 'cardinal',  // 摩羯座 - 啟動
    AQUARIUS: 'fixed',      // 水瓶座 - 固定
    PISCES: 'mutable',      // 雙魚座 - 變動
  };

  // 行星日夜屬性定義
  // 日間行星：太陽、木星、土星
  // 夜間行星：月亮、金星、火星
  // 水星則依據其相位與太陽的關係略做區分
  static const Map<int, bool> PLANET_SECT_NATURE = {
    SUN: true,      // 太陽 - 日間行星
    MOON: false,    // 月亮 - 夜間行星
    VENUS: false,   // 金星 - 夜間行星
    MARS: false,    // 火星 - 夜間行星
    JUPITER: true,  // 木星 - 日間行星
    SATURN: true,   // 土星 - 日間行星
    // 水星根據與太陽的關係決定，默認為中性
    MERCURY: true,  // 水星 - 默認為日間行星，但實際上會根據與太陽的關係調整
  };

  // 行星廟旺陷弱定義
  // 行星在各星座的廟旺陷弱狀態定義
  static const Map<int, Map<String, String>> PLANET_DIGNITIES = {
    // 太陽
    SUN: {
      DOMICILE: LEO, // 廟
      EXALTATION: ARIES, // 旺
      DETRIMENT: AQUARIUS, // 陷
      FALL: LIBRA, // 弱
    },
    // 月亮
    MOON: {
      DOMICILE: CANCER, // 廟
      EXALTATION: TAURUS, // 旺
      DETRIMENT: CAPRICORN, // 陷
      FALL: SCORPIO, // 弱
    },
    // 水星
    MERCURY: {
      DOMICILE: '$GEMINI,$VIRGO', // 廟
      EXALTATION: VIRGO, // 旺
      DETRIMENT: '$SAGITTARIUS,$PISCES', // 陷
      FALL: PISCES, // 弱
    },
    // 金星
    VENUS: {
      DOMICILE: '$TAURUS,$LIBRA', // 廟
      EXALTATION: PISCES, // 旺
      DETRIMENT: '$ARIES,$SCORPIO', // 陷
      FALL: VIRGO, // 弱
    },
    // 火星
    MARS: {
      DOMICILE: '$ARIES,$SCORPIO', // 廟
      EXALTATION: CAPRICORN, // 旺
      DETRIMENT: '$TAURUS,$LIBRA', // 陷
      FALL: CANCER, // 弱
    },
    // 木星
    JUPITER: {
      DOMICILE: '$SAGITTARIUS,$PISCES', // 廟
      EXALTATION: CANCER, // 旺
      DETRIMENT: '$GEMINI,$VIRGO', // 陷
      FALL: CAPRICORN, // 弱
    },
    // 土星
    SATURN: {
      DOMICILE: '$CAPRICORN,$AQUARIUS', // 廟
      EXALTATION: LIBRA, // 旺
      DETRIMENT: '$CANCER,$LEO', // 陷
      FALL: ARIES, // 弱
    },
    // 天王星
    URANUS: {
      DOMICILE: AQUARIUS, // 廟
      EXALTATION: SCORPIO, // 旺
      DETRIMENT: LEO, // 陷
      FALL: TAURUS, // 弱
    },
    // 海王星
    NEPTUNE: {
      DOMICILE: PISCES, // 廟
      EXALTATION: CANCER, // 旺
      DETRIMENT: VIRGO, // 陷
      FALL: CAPRICORN, // 弱
    },
    // 冥王星
    PLUTO: {
      DOMICILE: SCORPIO, // 廟
      EXALTATION: ARIES, // 旺
      DETRIMENT: TAURUS, // 陷
      FALL: LIBRA, // 弱
    },
  };

  // 廟旺陷弱定義常量
  static const String DOMICILE = 'domicile'; // 廟
  static const String EXALTATION = 'exaltation'; // 旺
  static const String DETRIMENT = 'detriment'; // 陷
  static const String FALL = 'fall'; // 弱

  // 年月常數定義
  static const double TROPICAL_YEAR = 365.24219; // 熱帶年（Tropical Year）天數
  static const double SIDEREAL_YEAR = 365.25636; // 恆星年（Sidereal Year）天數
  static const double NAIBOD_RATE = 0.9856; // Naibod 修正值（太陽平均每天移動 0°59'08"）
  static const double SIDEREAL_MONTH = 27.321661; // 恆星月（Sidereal Month）天數，月亮繪地球一圈的平均天數

  // 星座界主星定義（Terms/Bounds）
  // 每個星座被分為5個界，每個界由不同的行星主管
  // 格式：{起始度數字符串: 主管行星ID}
  static const Map<String, Map<String, int>> ZODIAC_TERMS = {
    ARIES: {
      '0': JUPITER,   // 0-6度：木星
      '6': VENUS,     // 6-12度：金星
      '12': MERCURY,  // 12-20度：水星
      '20': MARS,     // 20-25度：火星
      '25': SATURN,   // 25-30度：土星
    },
    TAURUS: {
      '0': VENUS,     // 0-8度：金星
      '8': MERCURY,   // 8-14度：水星
      '14': JUPITER,  // 14-22度：木星
      '22': SATURN,   // 22-27度：土星
      '27': MARS,     // 27-30度：火星
    },
    GEMINI: {
      '0': MERCURY,   // 0-6度：水星
      '6': JUPITER,   // 6-12度：木星
      '12': VENUS,    // 12-17度：金星
      '17': MARS,     // 17-24度：火星
      '24': SATURN,   // 24-30度：土星
    },
    CANCER: {
      '0': MARS,      // 0-7度：火星
      '7': VENUS,     // 7-13度：金星
      '13': MERCURY,  // 13-19度：水星
      '19': JUPITER,  // 19-26度：木星
      '26': SATURN,   // 26-30度：土星
    },
    LEO: {
      '0': JUPITER,   // 0-6度：木星
      '6': VENUS,     // 6-11度：金星
      '11': SATURN,   // 11-18度：土星
      '18': MERCURY,  // 18-24度：水星
      '24': MARS,     // 24-30度：火星
    },
    VIRGO: {
      '0': MERCURY,   // 0-7度：水星
      '7': VENUS,     // 7-17度：金星
      '17': JUPITER,  // 17-21度：木星
      '21': MARS,     // 21-28度：火星
      '28': SATURN,   // 28-30度：土星
    },
    LIBRA: {
      '0': SATURN,    // 0-6度：土星
      '6': MERCURY,   // 6-14度：水星
      '14': JUPITER,  // 14-21度：木星
      '21': VENUS,    // 21-28度：金星
      '28': MARS,     // 28-30度：火星
    },
    SCORPIO: {
      '0': MARS,      // 0-7度：火星
      '7': VENUS,     // 7-11度：金星
      '11': MERCURY,  // 11-19度：水星
      '19': JUPITER,  // 19-24度：木星
      '24': SATURN,   // 24-30度：土星
    },
    SAGITTARIUS: {
      '0': JUPITER,   // 0-12度：木星
      '12': VENUS,    // 12-17度：金星
      '17': MERCURY,  // 17-21度：水星
      '21': SATURN,   // 21-26度：土星
      '26': MARS,     // 26-30度：火星
    },
    CAPRICORN: {
      '0': MERCURY,   // 0-7度：水星
      '7': JUPITER,   // 7-14度：木星
      '14': VENUS,    // 14-22度：金星
      '22': SATURN,   // 22-26度：土星
      '26': MARS,     // 26-30度：火星
    },
    AQUARIUS: {
      '0': MERCURY,   // 0-7度：水星
      '7': VENUS,     // 7-13度：金星
      '13': JUPITER,  // 13-20度：木星
      '20': MARS,     // 20-25度：火星
      '25': SATURN,   // 25-30度：土星
    },
    PISCES: {
      '0': VENUS,     // 0-12度：金星
      '12': JUPITER,  // 12-16度：木星
      '16': MERCURY,  // 16-19度：水星
      '19': MARS,     // 19-28度：火星
      '28': SATURN,   // 28-30度：土星
    },
  };

  // 三分主星定義（Triplicity Rulers - Dorothean System）
  // 每個元素（火、土、風、水）的三分主星，分為日間主星、夜間主星和通用主星
  static const Map<String, Map<String, int>> ZODIAC_TRIPLICITIES = {
    // 火象星座：牡羊、獅子、射手
    // 火象三分性：日主星=太陽，夜主星=木星，通用主星=土星
    ARIES: {
      'day': SUN,       // 日間主星：太陽
      'night': JUPITER, // 夜間主星：木星
      'participating': SATURN, // 通用主星：土星
    },
    LEO: {
      'day': SUN,       // 日間主星：太陽
      'night': JUPITER, // 夜間主星：木星
      'participating': SATURN, // 通用主星：土星
    },
    SAGITTARIUS: {
      'day': SUN,       // 日間主星：太陽
      'night': JUPITER, // 夜間主星：木星
      'participating': SATURN, // 通用主星：土星
    },
    // 土象星座：金牛、處女、摩羯
    // 土象三分性：日主星=金星，夜主星=月亮，通用主星=火星
    TAURUS: {
      'day': VENUS,     // 日間主星：金星
      'night': MOON,    // 夜間主星：月亮
      'participating': MARS, // 通用主星：火星
    },
    VIRGO: {
      'day': VENUS,     // 日間主星：金星
      'night': MOON,    // 夜間主星：月亮
      'participating': MARS, // 通用主星：火星
    },
    CAPRICORN: {
      'day': VENUS,     // 日間主星：金星
      'night': MOON,    // 夜間主星：月亮
      'participating': MARS, // 通用主星：火星
    },
    // 風象星座：雙子、天秤、水瓶
    // 風象三分性：日主星=土星，夜主星=水星，通用主星=木星
    GEMINI: {
      'day': SATURN,    // 日間主星：土星
      'night': MERCURY, // 夜間主星：水星
      'participating': JUPITER, // 通用主星：木星
    },
    LIBRA: {
      'day': SATURN,    // 日間主星：土星
      'night': MERCURY, // 夜間主星：水星
      'participating': JUPITER, // 通用主星：木星
    },
    AQUARIUS: {
      'day': SATURN,    // 日間主星：土星
      'night': MERCURY, // 夜間主星：水星
      'participating': JUPITER, // 通用主星：木星
    },
    // 水象星座：巨蟹、天蠍、雙魚
    // 水象三分性：日主星=金星，夜主星=火星，通用主星=月亮
    CANCER: {
      'day': VENUS,     // 日間主星：金星
      'night': MARS,    // 夜間主星：火星
      'participating': MOON, // 通用主星：月亮
    },
    SCORPIO: {
      'day': VENUS,     // 日間主星：金星
      'night': MARS,    // 夜間主星：火星
      'participating': MOON, // 通用主星：月亮
    },
    PISCES: {
      'day': VENUS,     // 日間主星：金星
      'night': MARS,    // 夜間主星：火星
      'participating': MOON, // 通用主星：月亮
    },
  };

  // 十度主星定義（Decan Rulers）
  // 每個星座被分為3個十度，每個十度由不同的行星主管
  static const Map<String, Map<String, int>> ZODIAC_DECANS = {
    ARIES: {
      '0.0': MARS,    // 0-10度：火星
      '10.0': SUN,    // 10-20度：太陽
      '20.0': VENUS,  // 20-30度：金星
    },
    TAURUS: {
      '0.0': MERCURY, // 0-10度：水星
      '10.0': MOON,   // 10-20度：月亮
      '20.0': SATURN, // 20-30度：土星
    },
    GEMINI: {
      '0.0': JUPITER, // 0-10度：木星
      '10.0': MARS,   // 10-20度：火星
      '20.0': SUN,    // 20-30度：太陽
    },
    CANCER: {
      '0.0': VENUS,   // 0-10度：金星
      '10.0': MERCURY, // 10-20度：水星
      '20.0': MOON,   // 20-30度：月亮
    },
    LEO: {
      '0.0': SATURN,  // 0-10度：土星
      '10.0': JUPITER, // 10-20度：木星
      '20.0': MARS,   // 20-30度：火星
    },
    VIRGO: {
      '0.0': SUN,     // 0-10度：太陽
      '10.0': VENUS,  // 10-20度：金星
      '20.0': MERCURY, // 20-30度：水星
    },
    LIBRA: {
      '0.0': MOON,    // 0-10度：月亮
      '10.0': SATURN, // 10-20度：土星
      '20.0': JUPITER, // 20-30度：木星
    },
    SCORPIO: {
      '0.0': MARS,    // 0-10度：火星
      '10.0': SUN,    // 10-20度：太陽
      '20.0': VENUS,  // 20-30度：金星
    },
    SAGITTARIUS: {
      '0.0': MERCURY, // 0-10度：水星
      '10.0': MOON,   // 10-20度：月亮
      '20.0': SATURN, // 20-30度：土星
    },
    CAPRICORN: {
      '0.0': JUPITER, // 0-10度：木星
      '10.0': MARS,   // 10-20度：火星
      '20.0': SUN,    // 20-30度：太陽
    },
    AQUARIUS: {
      '0.0': VENUS,   // 0-10度：金星
      '10.0': MERCURY, // 10-20度：水星
      '20.0': MOON,   // 20-30度：月亮
    },
    PISCES: {
      '0.0': SATURN,  // 0-10度：土星
      '10.0': JUPITER, // 10-20度：木星
      '20.0': MARS,   // 20-30度：火星
    },
  };
}
class AspectMeaning {
  final String base;
  final String advice;
  final String tone;
  final String symbol;

  const AspectMeaning(this.base, this.advice, this.tone, this.symbol);
}

const Map<String, AspectMeaning> aspectDescriptions = {
  '合相': AspectMeaning(
    '能量融合，雙方特質加乘，有可能強化，也可能過度集中。',
    '可以善用這段時間展現你的特質，或與人建立深度連結。',
    '這是一個關鍵相位，會帶來明顯的內在或外在事件。',
    '🔆',
  ),
  '對分相': AspectMeaning(
    '對立能量帶來拉扯感，有助於看見不同觀點或反射他人。',
    '注意人際互動中可能出現的緊張感，學會從中取得平衡。',
    '這段時間你可能需要在兩種極端中找出自己的立場。',
    '🌗',
  ),
  '三分相': AspectMeaning(
    '能量流動順暢，有助於才能發揮與人際合作。',
    '這是適合推進計畫與展現能力的時機。',
    '輕鬆順利的星象氛圍，值得善加利用。',
    '🌈',
  ),
  '四分相': AspectMeaning(
    '帶來摩擦與壓力，是突破與行動的推力來源。',
    '別急著逃避壓力，這正是成長與修正方向的契機。',
    '你可能會感到卡住或焦躁，試著主動面對挑戰。',
    '⛅',
  ),
  '六分相': AspectMeaning(
    '雖不明顯，卻是積極互動與學習的契機。',
    '多與人交流，從細節中捕捉機會與靈感。',
    '這是潛藏支持與轉化的時期，別錯過微小機會。',
    '🌤️',
  ),
};

const Map<int, String> importanceToneMap = {
  5: '⚡ 這是一個強烈且深刻的相位，可能對你的人生節奏造成重要影響。',
  4: '⚡ 這是一個強烈且深刻的相位，可能對你的人生節奏造成重要影響。',
  3: '✨ 這個相位可能帶來實質事件或心理觸發，值得注意。',
  2: '🔍 這個相位雖不劇烈，但會在互動與情緒中留下痕跡。',
  1: '☁️ 整體影響溫和，可以當作一種氛圍來感受。',
};

String _getPlanetDescription(String planetName) {
  const planetDescriptions = {
    '太陽': '核心自我、生命力、意識',
    '月亮': '情感、潛意識、本能反應',
    '水星': '思維、溝通、學習能力',
    '金星': '愛情、美感、價值觀',
    '火星': '行動力、慈望、競爭力',
    '木星': '擴張、幸運、信念',
    '土星': '限制、責任、紀律',
    '天王星': '變革、獨創、突破',
    '海王星': '靈性、幻想、溶解',
    '冥王星': '轉化、權力、重生',
    '北交點': '命運方向、靈魂成長',
    '南交點': '過去業力、舒適區',
    '上升': '自我表達、外在形象、人格面具',
    '中天': '事業、社會地位、公眾形象',
    '下降': '人際關係、伴侶、他人投射',
    '天底': '家庭、根源、內在安全感',
  };
  return planetDescriptions[planetName] ?? '行星特質';
}

String _getHouseDescription(int houseNumber) {
  const houseDescriptions = {
    1: '自我、外表、個性、生命力',
    2: '財產、價值觀、資源、安全感',
    3: '溝通、短途旅行、兄弟姐妹、早期教育',
    4: '家庭、根源、父母、內在情感基礎',
    5: '創造力、娛樂、子女、浪漫關係',
    6: '健康、工作、日常生活、服務',
    7: '關係、婚姻、合作夥伴、公開的敵人',
    8: '共享資源、轉變、性、死亡與重生',
    9: '高等教育、哲學、長途旅行、信仰',
    10: '職業、社會地位、名聲、權威',
    11: '友誼、團體、願望、社會理想',
    12: '潛意識、秘密、限制、靈性成長',
  };
  return houseDescriptions[houseNumber.clamp(1, 12)] ?? '宮位特質';
}

int _getAspectImportance(String aspect, String planet1, String planet2) {
  final majorPersonalPoints = ['太陽', '月亮'];
  final innerPlanets = ['太陽', '月亮', '水星', '金星', '火星'];
  final outerPlanets = ['木星', '土星', '天王星', '海王星', '冥王星'];

  final aspectWeights = {
    '合相': 4,
    '對分相': 3,
    '四分相': 2,
    '三分相': 2,
    '六分相': 1,
  };

  int aspectWeight = aspectWeights[aspect] ?? 1;

  bool involvesMajorPoint =
      majorPersonalPoints.contains(planet1) || majorPersonalPoints.contains(planet2);
  bool isInnerAspect =
      innerPlanets.contains(planet1) && innerPlanets.contains(planet2);
  bool isMixedAspect =
      (innerPlanets.contains(planet1) && outerPlanets.contains(planet2)) ||
          (outerPlanets.contains(planet1) && innerPlanets.contains(planet2));

  int importance = aspectWeight;
  if (involvesMajorPoint) importance += 1;
  if (isMixedAspect) importance += 1;
  if (isInnerAspect) importance += 1;

  return importance.clamp(1, 5);
}

import 'package:flutter/material.dart';

import '../core/constants/astrology_constants.dart';
import '../data/models/astrology/planet.dart';

/// 行星計算測試範例
/// 
/// 展示修復後的行星數據結構和 HeavenlyBody 映射
class PlanetCalculationTest {
  
  /// 測試行星數據結構
  static void testPlanetDataStructure() {
    print('=== 行星數據結構測試 ===\n');
    
    // 1. 測試新的類型安全行星列表
    print('類型安全的行星列表：');
    final typedPlanets = AstrologyConstants.PLANETS_TYPED;
    for (final planet in typedPlanets) {
      print('  ${planet.name} (${planet.symbol}) - ID: ${planet.id}');
      print('    類別: ${planet.category}');
      print('    HeavenlyBody: ${planet.getHeavenlyBody()}');
      print('    關鍵詞: ${planet.keywords.join(', ')}');
      print('');
    }
    
    // 2. 測試向後兼容的 Map 格式
    print('向後兼容的 Map 格式：');
    final mapPlanets = AstrologyConstants.PLANETS;
    for (final planet in mapPlanets) {
      print('  ${planet['name']} (${planet['symbol']}) - ID: ${planet['id']}');
      print('    HeavenlyBody: ${planet['body']}');
      print('    顏色: ${planet['color']}');
      print('');
    }
  }
  
  /// 測試 HeavenlyBody 映射
  static void testHeavenlyBodyMapping() {
    print('=== HeavenlyBody 映射測試 ===\n');
    
    final planets = AstrologyConstants.PLANETS_TYPED;
    
    print('行星 ID 到 HeavenlyBody 的映射：');
    for (final planet in planets) {
      final heavenlyBody = planet.getHeavenlyBody();
      print('  ID ${planet.id} (${planet.name}) → ${heavenlyBody}');
    }
    print('');
    
    // 測試特殊情況
    print('特殊情況測試：');
    
    // 測試未知 ID
    final unknownPlanet = Planet(
      id: 999,
      name: '未知行星',
      englishName: 'Unknown Planet',
      symbol: '?',
      description: '測試用未知行星',
      color: Colors.grey,
      category: 'unknown',
    );
    
    print('  未知行星 (ID: 999) → ${unknownPlanet.getHeavenlyBody()}');
    print('');
  }
  
  /// 測試 toMap 方法的完整性
  static void testToMapMethod() {
    print('=== toMap 方法測試 ===\n');
    
    final sun = AstrologyConstants.PLANETS_TYPED.first;
    final sunMap = sun.toMap();
    
    print('太陽的 Map 表示：');
    sunMap.forEach((key, value) {
      print('  $key: $value (${value.runtimeType})');
    });
    print('');
    
    // 檢查必要的鍵是否存在
    final requiredKeys = ['id', 'name', 'symbol', 'body', 'color'];
    print('必要鍵檢查：');
    for (final key in requiredKeys) {
      final exists = sunMap.containsKey(key);
      final status = exists ? '✓' : '✗';
      print('  $status $key: ${exists ? sunMap[key] : '缺失'}');
    }
    print('');
  }
  
  /// 測試類型轉換
  static void testTypeCasting() {
    print('=== 類型轉換測試 ===\n');
    
    final planets = AstrologyConstants.PLANETS;
    
    print('測試從 Map 中提取 HeavenlyBody：');
    for (final planet in planets.take(3)) { // 只測試前3個
      try {
        final name = planet['name'] as String;
        final body = planet['body']; // 不進行類型轉換，直接獲取
        print('  ✓ ${name}: ${body} (${body.runtimeType})');
        
        // 測試實際的類型轉換
        if (body is int) {
          print('    ✗ 錯誤：body 是 int 類型，應該是 HeavenlyBody');
        } else {
          print('    ✓ 正確：body 是 HeavenlyBody 類型');
        }
      } catch (e) {
        print('  ✗ 錯誤：${planet['name']} - $e');
      }
    }
    print('');
  }
  
  /// 測試行星分類
  static void testPlanetCategories() {
    print('=== 行星分類測試 ===\n');
    
    final planets = AstrologyConstants.PLANETS_TYPED;
    
    // 按類別分組
    final categories = <String, List<Planet>>{};
    for (final planet in planets) {
      categories.putIfAbsent(planet.category, () => []).add(planet);
    }
    
    print('按類別分組的行星：');
    categories.forEach((category, planetList) {
      print('  $category:');
      for (final planet in planetList) {
        print('    - ${planet.name} (${planet.symbol})');
      }
      print('');
    });
  }
  
  /// 測試便利方法
  static void testConvenienceMethods() {
    print('=== 便利方法測試 ===\n');
    
    final planets = AstrologyConstants.PLANETS_TYPED;
    
    print('行星分類便利方法：');
    for (final planet in planets) {
      final classifications = <String>[];
      
      if (planet.isLuminary) classifications.add('發光體');
      if (planet.isInnerPlanet) classifications.add('內行星');
      if (planet.isOuterPlanet) classifications.add('外行星');
      if (planet.isModernPlanet) classifications.add('現代行星');
      
      if (classifications.isNotEmpty) {
        print('  ${planet.name}: ${classifications.join(', ')}');
      }
    }
    print('');
  }
  
  /// 運行所有測試
  static void runAllTests() {
    print('🧪 開始行星計算測試\n');
    
    testPlanetDataStructure();
    testHeavenlyBodyMapping();
    testToMapMethod();
    testTypeCasting();
    testPlanetCategories();
    testConvenienceMethods();
    
    print('✅ 所有測試完成！');
  }
}
